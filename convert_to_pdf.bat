@echo off
echo 🌱 种子分析系统流程图转换工具
echo ================================

cd workflow_diagrams

echo 📄 转换PNG图片为PDF格式...

for %%f in (*.png) do (
    echo 🔄 转换: %%f
    magick "%%f" "%%~nf.pdf" 2>nul
    if exist "%%~nf.pdf" (
        echo ✅ 成功: %%~nf.pdf
    ) else (
        echo ❌ 失败: %%f
    )
)

echo.
echo 📊 转换完成！
echo 📁 文件位置: workflow_diagrams/
dir /b *.pdf 2>nul
if errorlevel 1 (
    echo ⚠️  未找到PDF文件，可能需要安装ImageMagick
    echo 💡 或者直接使用PNG文件
)

pause
