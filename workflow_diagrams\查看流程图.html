<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌱 种子分析系统流程图查看器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2e7d32;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .diagram-section {
            margin-bottom: 50px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .diagram-header {
            background: linear-gradient(135deg, #4caf50, #2e7d32);
            color: white;
            padding: 15px 20px;
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .diagram-content {
            padding: 20px;
        }
        
        .diagram-image {
            width: 100%;
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .diagram-image:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .diagram-description {
            color: #666;
            font-size: 0.95em;
            margin-bottom: 10px;
        }
        
        .features {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .features h4 {
            color: #2e7d32;
            margin-top: 0;
        }
        
        .features ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .features li {
            margin-bottom: 5px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }
        
        .modal-content {
            margin: auto;
            display: block;
            width: 90%;
            max-width: 1400px;
            max-height: 90%;
            object-fit: contain;
        }
        
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #bbb;
        }
        
        .download-section {
            background: #e8f5e9;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
            text-align: center;
        }
        
        .download-btn {
            display: inline-block;
            background: #4caf50;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .download-btn:hover {
            background: #45a049;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌱 种子分析系统流程图</h1>
        
        <div class="diagram-section">
            <div class="diagram-header">
                📊 主工作流程图
            </div>
            <div class="diagram-content">
                <img src="种子分析系统主工作流程.png" alt="种子分析系统主工作流程" class="diagram-image" onclick="openModal(this)">
                <div class="diagram-description">
                    展示完整的种子分析管道，从原始图像输入到最终结果输出的全过程。
                </div>
                <div class="features">
                    <h4>🎯 主要特性:</h4>
                    <ul>
                        <li>📁 数据输入和按类别组织</li>
                        <li>🔍 多种图像分割方法选择</li>
                        <li>🎯 YOLO目标检测训练流程</li>
                        <li>🧠 分类模型训练管道</li>
                        <li>🔄 检测和分类的集成推理</li>
                        <li>📊 最终结果输出和统计</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="diagram-section">
            <div class="diagram-header">
                🔄 数据流程图
            </div>
            <div class="diagram-content">
                <img src="种子分析数据流程图.png" alt="种子分析数据流程图" class="diagram-image" onclick="openModal(this)">
                <div class="diagram-description">
                    详细展示数据在各个处理阶段的转换过程和文件格式。
                </div>
                <div class="features">
                    <h4>📂 数据流程:</h4>
                    <ul>
                        <li>📂 输入数据的目录结构</li>
                        <li>🔍 分割输出的文件格式</li>
                        <li>🎯 YOLO数据集的组织方式</li>
                        <li>🗂️ 分类数据集的结构</li>
                        <li>🤖 训练模型的存储格式</li>
                        <li>📊 推理管道的数据流向</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="diagram-section">
            <div class="diagram-header">
                🚀 快速开始指南
            </div>
            <div class="diagram-content">
                <img src="种子分析快速开始指南.png" alt="种子分析快速开始指南" class="diagram-image" onclick="openModal(this)">
                <div class="diagram-description">
                    7个步骤完成系统配置，包含详细的参数设置和时间估计。
                </div>
                <div class="features">
                    <h4>⏱️ 快速上手:</h4>
                    <ul>
                        <li>📁 步骤1: 数据准备和组织</li>
                        <li>🔍 步骤2: 图像分割配置</li>
                        <li>🎯 步骤3-4: YOLO训练设置</li>
                        <li>📂 步骤5-6: 分类训练配置</li>
                        <li>🔄 步骤7: 集成推理设置</li>
                        <li>⏱️ 每个步骤的时间估计</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="diagram-section">
            <div class="diagram-header">
                🌐 English Version
            </div>
            <div class="diagram-content">
                <img src="Seed_Analysis_System_Workflow.png" alt="Seed Analysis System Workflow" class="diagram-image" onclick="openModal(this)">
                <div class="diagram-description">
                    English version of the main workflow diagram for international users.
                </div>
                <div class="features">
                    <h4>🎯 Features:</h4>
                    <ul>
                        <li>📁 Same functionality as Chinese version</li>
                        <li>🌐 English labels and descriptions</li>
                        <li>🤝 Suitable for international collaboration</li>
                        <li>📄 Ready for academic publications</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="download-section">
            <h3>📥 下载流程图</h3>
            <p>点击下面的链接下载高清PNG格式的流程图：</p>
            <a href="种子分析系统主工作流程.png" download class="download-btn">📊 主工作流程图</a>
            <a href="种子分析数据流程图.png" download class="download-btn">🔄 数据流程图</a>
            <a href="种子分析快速开始指南.png" download class="download-btn">🚀 快速开始指南</a>
            <a href="Seed_Analysis_System_Workflow.png" download class="download-btn">🌐 English Version</a>
        </div>
        
        <div class="footer">
            <p>🌱 种子分析系统 - 基于深度学习的智能种子检测与分类平台</p>
            <p>💡 提示：点击图片可以放大查看，右键保存可下载图片</p>
        </div>
    </div>
    
    <!-- 模态框 -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>
    
    <script>
        function openModal(img) {
            var modal = document.getElementById("imageModal");
            var modalImg = document.getElementById("modalImage");
            modal.style.display = "block";
            modalImg.src = img.src;
        }
        
        function closeModal() {
            var modal = document.getElementById("imageModal");
            modal.style.display = "none";
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            var modal = document.getElementById("imageModal");
            if (event.target == modal) {
                modal.style.display = "none";
            }
        }
        
        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
