graph TD
    Start([🚀 开始使用种子分析系统]) --> Step1[📁 步骤1: 准备数据<br/>按类别组织种子图像]
    
    Step1 --> Step2[🔍 步骤2: 图像分割<br/>选择透明背景分割<br/>✅ 启用YOLO标注]
    
    Step2 --> Step3[🎯 步骤3: YOLO训练<br/>创建数据集<br/>✅ 启用种子检测优化模式]
    
    Step3 --> Step4[🏋️ 步骤4: 训练YOLO模型<br/>选择yolov8n<br/>训练50轮]
    
    Step4 --> Step5[📂 步骤5: 准备分类数据<br/>从分割结果自动生成<br/>按类别组织]
    
    Step5 --> Step6[🧠 步骤6: 训练分类模型<br/>选择ResNet18或EfficientNet<br/>训练50轮]
    
    Step6 --> Step7[🔄 步骤7: 集成推理<br/>加载两个训练好的模型<br/>检测+分类]
    
    Step7 --> Result[📊 完成!<br/>可以分析新的种子图像了]
    
    %% 详细说明分支
    Step1 --> Detail1[📋 数据组织示例:<br/>wheat/ - 小麦图像<br/>corn/ - 玉米图像<br/>rice/ - 水稻图像<br/>soybean/ - 大豆图像]
    
    Step2 --> Detail2[⚙️ 分割设置:<br/>• 透明背景分割<br/>• Alpha阈值: 50<br/>• 最小种子面积: 200<br/>• 生成YOLO标注: ✅]
    
    Step3 --> Detail3[🎯 YOLO设置:<br/>• 种子检测优化: ✅<br/>• 单类别: 'seed'<br/>• 自动数据分割<br/>• 专业增强策略]
    
    Step4 --> Detail4[🏋️ 训练参数:<br/>• 模型: yolov8n<br/>• 轮数: 50<br/>• 批次: 8<br/>• 图像尺寸: 640]
    
    Step5 --> Detail5[📂 分类数据:<br/>• 自动提取种子图像<br/>• 文件夹名=类别标签<br/>• 过滤特殊文件<br/>• 生成数据集信息]
    
    Step6 --> Detail6[🧠 分类训练:<br/>• ResNet18/EfficientNet<br/>• 迁移学习<br/>• 学习率: 0.001<br/>• 批次: 32]
    
    Step7 --> Detail7[🔄 推理流程:<br/>1. YOLO检测种子位置<br/>2. 裁剪检测到的种子<br/>3. 分类模型识别类型<br/>4. 输出结果和统计]
    
    %% 时间估计
    Step2 -.-> Time1[⏱️ 约5-10分钟<br/>取决于图像数量]
    Step4 -.-> Time2[⏱️ 约15-30分钟<br/>RTX 4090]
    Step6 -.-> Time3[⏱️ 约10-20分钟<br/>RTX 4090]
    
    %% 样式设置
    classDef mainStep fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    classDef detail fill:#f1f8e9,stroke:#388e3c,stroke-width:2px,color:#000
    classDef time fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef start fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000
    classDef result fill:#fce4ec,stroke:#c2185b,stroke-width:3px,color:#000
    
    class Start start
    class Step1,Step2,Step3,Step4,Step5,Step6,Step7 mainStep
    class Detail1,Detail2,Detail3,Detail4,Detail5,Detail6,Detail7 detail
    class Time1,Time2,Time3 time
    class Result result
