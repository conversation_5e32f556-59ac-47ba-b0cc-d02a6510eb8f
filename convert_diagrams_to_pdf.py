#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将流程图PNG文件转换为PDF格式
Convert workflow diagram PNG files to PDF format
"""

import os
import sys
from PIL import Image
import glob

def png_to_pdf(png_path, pdf_path):
    """将PNG文件转换为PDF"""
    try:
        # 打开PNG图像
        image = Image.open(png_path)
        
        # 如果图像有透明通道，转换为RGB
        if image.mode in ('RGBA', 'LA'):
            # 创建白色背景
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'RGBA':
                background.paste(image, mask=image.split()[-1])  # 使用alpha通道作为mask
            else:
                background.paste(image, mask=image.split()[-1])
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 保存为PDF
        image.save(pdf_path, 'PDF', resolution=300.0, quality=95)
        print(f"✅ 转换成功: {os.path.basename(png_path)} -> {os.path.basename(pdf_path)}")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败 {png_path}: {e}")
        return False

def convert_all_diagrams():
    """转换所有流程图"""
    # 确保目录存在
    diagrams_dir = "workflow_diagrams"
    if not os.path.exists(diagrams_dir):
        print(f"❌ 目录不存在: {diagrams_dir}")
        return
    
    # 查找所有PNG文件
    png_files = glob.glob(os.path.join(diagrams_dir, "*.png"))
    
    if not png_files:
        print(f"❌ 在 {diagrams_dir} 中未找到PNG文件")
        return
    
    print(f"🔄 找到 {len(png_files)} 个PNG文件，开始转换...")
    
    success_count = 0
    for png_file in png_files:
        # 生成PDF文件名
        pdf_file = png_file.replace('.png', '.pdf')
        
        if png_to_pdf(png_file, pdf_file):
            success_count += 1
    
    print(f"\n📊 转换完成: {success_count}/{len(png_files)} 个文件成功转换")
    
    # 列出生成的文件
    print("\n📁 生成的文件:")
    all_files = glob.glob(os.path.join(diagrams_dir, "*"))
    for file_path in sorted(all_files):
        file_size = os.path.getsize(file_path) / 1024  # KB
        print(f"  📄 {os.path.basename(file_path)} ({file_size:.1f} KB)")

def main():
    """主函数"""
    print("🌱 种子分析系统流程图转换工具")
    print("=" * 50)
    
    try:
        convert_all_diagrams()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
