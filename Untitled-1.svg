<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1451.878173828125 1583" style="max-width: 1451.878173828125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0"><style>#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .error-icon{fill:#a44141;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .edge-thickness-normal{stroke-width:1px;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .marker.cross{stroke:lightgrey;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 p{margin:0;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .cluster-label text{fill:#F9FFFE;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .cluster-label span{color:#F9FFFE;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .cluster-label span p{background-color:transparent;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .label text,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 span{fill:#ccc;color:#ccc;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .node rect,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .node circle,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .node ellipse,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .node polygon,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .rough-node .label text,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .node .label text,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .image-shape .label,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .icon-shape .label{text-anchor:middle;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .rough-node .label,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .node .label,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .image-shape .label,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .icon-shape .label{text-align:center;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .node.clickable{cursor:pointer;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .arrowheadPath{fill:lightgrey;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .cluster text{fill:#F9FFFE;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .cluster span{color:#F9FFFE;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 rect.text{fill:none;stroke-width:0;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .icon-shape,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .icon-shape p,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .icon-shape rect,#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .mainStep&gt;*{fill:#e3f2fd!important;stroke:#1976d2!important;stroke-width:3px!important;color:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .mainStep span{fill:#e3f2fd!important;stroke:#1976d2!important;stroke-width:3px!important;color:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .mainStep tspan{fill:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .detail&gt;*{fill:#f1f8e9!important;stroke:#388e3c!important;stroke-width:2px!important;color:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .detail span{fill:#f1f8e9!important;stroke:#388e3c!important;stroke-width:2px!important;color:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .detail tspan{fill:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .time&gt;*{fill:#fff3e0!important;stroke:#f57c00!important;stroke-width:2px!important;color:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .time span{fill:#fff3e0!important;stroke:#f57c00!important;stroke-width:2px!important;color:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .time tspan{fill:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .start&gt;*{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:3px!important;color:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .start span{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:3px!important;color:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .start tspan{fill:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .result&gt;*{fill:#fce4ec!important;stroke:#c2185b!important;stroke-width:3px!important;color:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .result span{fill:#fce4ec!important;stroke:#c2185b!important;stroke-width:3px!important;color:#000!important;}#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0 .result tspan{fill:#000!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Start_Step1_0" d="M1338.106,47L1338.106,51.167C1338.106,55.333,1338.106,63.667,1338.106,71.333C1338.106,79,1338.106,86,1338.106,89.5L1338.106,93"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step1_Step2_1" d="M1236.106,161.725L1210.812,168.104C1185.519,174.483,1134.931,187.242,1109.637,201.121C1084.344,215,1084.344,230,1084.344,237.5L1084.344,245"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step2_Step3_2" d="M983.406,336.363L953.966,346.97C924.526,357.576,865.645,378.788,836.204,396.894C806.764,415,806.764,430,806.764,437.5L806.764,445"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step3_Step4_3" d="M737.59,551L726.513,559.167C715.436,567.333,693.282,583.667,682.205,599.333C671.128,615,671.128,630,671.128,637.5L671.128,645"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step4_Step5_4" d="M606.491,751L596.141,759.167C585.79,767.333,565.09,783.667,554.739,799.333C544.389,815,544.389,830,544.389,837.5L544.389,845"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step5_Step6_5" d="M471.764,951L460.135,959.167C448.505,967.333,425.246,983.667,413.617,997.333C401.987,1011,401.987,1022,401.987,1027.5L401.987,1033"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step6_Step7_6" d="M314.721,1163L306.179,1169.167C297.637,1175.333,280.553,1187.667,272.011,1201.333C263.469,1215,263.469,1230,263.469,1237.5L263.469,1245"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step7_Result_7" d="M193.36,1351L182.133,1359.167C170.906,1367.333,148.453,1383.667,137.227,1401.333C126,1419,126,1438,126,1447.5L126,1457"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step1_Detail1_8" d="M1338.106,175L1338.106,179.167C1338.106,183.333,1338.106,191.667,1338.106,199.333C1338.106,207,1338.106,214,1338.106,217.5L1338.106,221"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step2_Detail2_9" d="M1084.421,351L1084.433,359.167C1084.446,367.333,1084.471,383.667,1084.483,395.333C1084.495,407,1084.495,414,1084.495,417.5L1084.495,421"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step3_Detail3_10" d="M875.938,551L887.015,559.167C898.092,567.333,920.246,583.667,931.323,595.333C942.4,607,942.4,614,942.4,617.5L942.4,621"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step4_Detail4_11" d="M735.765,751L746.115,759.167C756.466,767.333,777.166,783.667,787.517,795.333C797.867,807,797.867,814,797.867,817.5L797.867,821"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step5_Detail5_12" d="M617.014,951L628.643,959.167C640.273,967.333,663.532,983.667,675.161,995.333C686.791,1007,686.791,1014,686.791,1017.5L686.791,1021"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step6_Detail6_13" d="M489.254,1163L497.796,1169.167C506.338,1175.333,523.422,1187.667,531.964,1197.333C540.506,1207,540.506,1214,540.506,1217.5L540.506,1221"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step7_Detail7_14" d="M333.578,1351L344.804,1359.167C356.031,1367.333,378.484,1383.667,389.711,1395.333C400.938,1407,400.938,1414,400.938,1417.5L400.938,1421"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step2_Time1_15" d="M1185.281,341.972L1208.539,351.644C1231.797,361.315,1278.314,380.657,1301.572,399.829C1324.83,419,1324.83,438,1324.83,447.5L1324.83,457"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step4_Time2_16" d="M791.462,734.342L829.806,745.285C868.149,756.228,944.836,778.114,983.18,798.557C1021.523,819,1021.523,838,1021.523,847.5L1021.523,857"></path><path marker-end="url(#mermaid-34a52083-87fd-4440-aa03-b5b97b46eeb0_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Step6_Time3_17" d="M531.987,1133.107L575.766,1144.255C619.545,1155.404,707.102,1177.702,750.881,1198.351C794.659,1219,794.659,1238,794.659,1247.5L794.659,1257"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1338.106243133545, 27.5)" id="flowchart-Start-800" class="node default start"><rect height="39" width="211.5437469482422" y="-19.5" x="-105.7718734741211" ry="19.5" rx="19.5" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-93.3968734741211, -12)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="24" width="186.7937469482422"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>🚀 开始使用种子分析系统</p></span></div></foreignObject></g></g><g transform="translate(1338.106243133545, 136)" id="flowchart-Step1-801" class="node default mainStep"><rect height="78" width="204" y="-39" x="-102" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-72, -24)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="48" width="144"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>📁 步骤1: 准备数据<br>按类别组织种子图像</p></span></div></foreignObject></g></g><g transform="translate(1084.3437461853027, 300)" id="flowchart-Step2-803" class="node default mainStep"><rect height="102" width="201.875" y="-51" x="-100.9375" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-70.9375, -36)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="72" width="141.875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>🔍 步骤2: 图像分割<br>选择透明背景分割<br>✅ 启用YOLO标注</p></span></div></foreignObject></g></g><g transform="translate(806.7640609741211, 500)" id="flowchart-Step3-805" class="node default mainStep"><rect height="102" width="246.7937469482422" y="-51" x="-123.3968734741211" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-93.3968734741211, -36)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="72" width="186.7937469482422"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>🎯 步骤3: YOLO训练<br>创建数据集<br>✅ 启用种子检测优化模式</p></span></div></foreignObject></g></g><g transform="translate(671.1281242370605, 700)" id="flowchart-Step4-807" class="node default mainStep"><rect height="102" width="240.6687469482422" y="-51" x="-120.3343734741211" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-90.3343734741211, -36)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="72" width="180.6687469482422"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>🏋️ 步骤4: 训练YOLO模型<br>选择yolov8n<br>训练50轮</p></span></div></foreignObject></g></g><g transform="translate(544.3890609741211, 900)" id="flowchart-Step5-809" class="node default mainStep"><rect height="102" width="233.875" y="-51" x="-116.9375" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-86.9375, -36)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="72" width="173.875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>📂 步骤5: 准备分类数据<br>从分割结果自动生成<br>按类别组织</p></span></div></foreignObject></g></g><g transform="translate(401.98749923706055, 1100)" id="flowchart-Step6-811" class="node default mainStep"><rect height="126" width="260" y="-63" x="-130" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-100, -48)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="96" width="200"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;"><span class="nodeLabel" style="color:#000 !important"><p>🧠 步骤6: 训练分类模型<br>选择ResNet18或EfficientNet<br>训练50轮</p></span></div></foreignObject></g></g><g transform="translate(263.46875, 1300)" id="flowchart-Step7-813" class="node default mainStep"><rect height="102" width="220" y="-51" x="-110" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-80, -36)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="72" width="160"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>🔄 步骤7: 集成推理<br>加载两个训练好的模型<br>检测+分类</p></span></div></foreignObject></g></g><g transform="translate(126, 1500)" id="flowchart-Result-815" class="node default result"><rect height="78" width="236" y="-39" x="-118" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-88, -24)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="48" width="176"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>📊 完成!<br>可以分析新的种子图像了</p></span></div></foreignObject></g></g><g transform="translate(1338.106243133545, 300)" id="flowchart-Detail1-817" class="node default detail"><rect height="150" width="205.65000915527344" y="-75" x="-102.82500457763672" style="fill:#f1f8e9 !important;stroke:#388e3c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-72.82500457763672, -60)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="120" width="145.65000915527344"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>📋 数据组织示例:<br>wheat/ - 小麦图像<br>corn/ - 玉米图像<br>rice/ - 水稻图像<br>soybean/ - 大豆图像</p></span></div></foreignObject></g></g><g transform="translate(1084.4953079223633, 500)" id="flowchart-Detail2-819" class="node default detail"><rect height="150" width="208.6687469482422" y="-75" x="-104.3343734741211" style="fill:#f1f8e9 !important;stroke:#388e3c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-74.3343734741211, -60)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="120" width="148.6687469482422"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>⚙️ 分割设置:<br>• 透明背景分割<br>• Alpha阈值: 50<br>• 最小种子面积: 200<br>• 生成YOLO标注: ✅</p></span></div></foreignObject></g></g><g transform="translate(942.3999977111816, 700)" id="flowchart-Detail3-821" class="node default detail"><rect height="150" width="201.875" y="-75" x="-100.9375" style="fill:#f1f8e9 !important;stroke:#388e3c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-70.9375, -60)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="120" width="141.875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>🎯 YOLO设置:<br>• 种子检测优化: ✅<br>• 单类别: 'seed'<br>• 自动数据分割<br>• 专业增强策略</p></span></div></foreignObject></g></g><g transform="translate(797.8671875, 900)" id="flowchart-Detail4-823" class="node default detail"><rect height="150" width="173.0812530517578" y="-75" x="-86.5406265258789" style="fill:#f1f8e9 !important;stroke:#388e3c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-56.540626525878906, -60)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="120" width="113.08125305175781"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>🏋️ 训练参数:<br>• 模型: yolov8n<br>• 轮数: 50<br>• 批次: 8<br>• 图像尺寸: 640</p></span></div></foreignObject></g></g><g transform="translate(686.7906227111816, 1100)" id="flowchart-Detail5-825" class="node default detail"><rect height="150" width="209.6062469482422" y="-75" x="-104.8031234741211" style="fill:#f1f8e9 !important;stroke:#388e3c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-74.8031234741211, -60)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="120" width="149.6062469482422"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>📂 分类数据:<br>• 自动提取种子图像<br>• 文件夹名=类别标签<br>• 过滤特殊文件<br>• 生成数据集信息</p></span></div></foreignObject></g></g><g transform="translate(540.5062484741211, 1300)" id="flowchart-Detail6-827" class="node default detail"><rect height="150" width="234.0749969482422" y="-75" x="-117.0374984741211" style="fill:#f1f8e9 !important;stroke:#388e3c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-87.0374984741211, -60)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="120" width="174.0749969482422"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>🧠 分类训练:<br>• ResNet18/EfficientNet<br>• 迁移学习<br>• 学习率: 0.001<br>• 批次: 32</p></span></div></foreignObject></g></g><g transform="translate(400.9375, 1500)" id="flowchart-Detail7-829" class="node default detail"><rect height="150" width="213.875" y="-75" x="-106.9375" style="fill:#f1f8e9 !important;stroke:#388e3c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-76.9375, -60)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="120" width="153.875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>🔄 推理流程:<br>1. YOLO检测种子位置<br>2. 裁剪检测到的种子<br>3. 分类模型识别类型<br>4. 输出结果和统计</p></span></div></foreignObject></g></g><g transform="translate(1324.8296813964844, 500)" id="flowchart-Time1-831" class="node default time"><rect height="78" width="172" y="-39" x="-86" style="fill:#fff3e0 !important;stroke:#f57c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-56, -24)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="48" width="112"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>⏱️ 约5-10分钟<br>取决于图像数量</p></span></div></foreignObject></g></g><g transform="translate(1021.5234375, 900)" id="flowchart-Time2-833" class="node default time"><rect height="78" width="174.23125457763672" y="-39" x="-87.11562728881836" style="fill:#fff3e0 !important;stroke:#f57c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-57.11562728881836, -24)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="48" width="114.23125457763672"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>⏱️ 约15-30分钟<br>RTX 4090</p></span></div></foreignObject></g></g><g transform="translate(794.6593704223633, 1300)" id="flowchart-Time3-835" class="node default time"><rect height="78" width="174.23125457763672" y="-39" x="-87.11562728881836" style="fill:#fff3e0 !important;stroke:#f57c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-57.11562728881836, -24)" style="color:#000 !important" class="label"><rect></rect><foreignObject height="48" width="114.23125457763672"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>⏱️ 约10-20分钟<br>RTX 4090</p></span></div></foreignObject></g></g></g></g></g></svg>