#!/usr/bin/env python3
"""
测试分类数据准备修复
"""

import os
import sys
from pathlib import Path

def create_mock_segmentation_output():
    """创建模拟的分割输出结构"""
    print("创建模拟分割输出结构...")
    
    # 创建测试目录
    test_output_dir = Path("test_segmentation_output")
    test_output_dir.mkdir(exist_ok=True)
    
    # 创建不同种子类别的文件夹
    seed_classes = ["wheat", "corn", "rice"]
    
    for seed_class in seed_classes:
        class_dir = test_output_dir / seed_class
        class_dir.mkdir(exist_ok=True)
        
        # 创建模拟的种子图像文件
        for i in range(3):
            seed_file = class_dir / f"seed_{i+1}.jpg"
            seed_file.touch()  # 创建空文件
        
        # 创建一些应该被忽略的文件
        (class_dir / "transparent_segmentation.png").touch()
        (class_dir / "marked_image.jpg").touch()
        (class_dir / "original_image.jpg").touch()
        
        # 创建marked_crops文件夹
        marked_crops_dir = class_dir / "marked_crops"
        marked_crops_dir.mkdir(exist_ok=True)
        (marked_crops_dir / "crop_1.jpg").touch()
        
        print(f"✅ 创建类别文件夹: {seed_class} (3个种子图像)")
    
    print(f"✅ 模拟分割输出创建完成: {test_output_dir}")
    return str(test_output_dir)

def test_classification_data_preparation():
    """测试分类数据准备"""
    print("\n🧪 测试分类数据准备...")
    
    try:
        # 导入必要的模块
        from classification_manager import ClassificationManager
        from classification_trainer import ClassificationTrainer
        
        print("✅ 模块导入成功")
        
        # 创建管理器和训练器
        manager = ClassificationManager()
        trainer = ClassificationTrainer(manager)
        
        print("✅ 训练器创建成功")
        
        # 创建模拟数据
        segmentation_dir = create_mock_segmentation_output()
        classification_dir = "test_classification_data"
        
        # 测试数据准备
        print(f"\n📁 测试数据准备:")
        print(f"  分割结果目录: {segmentation_dir}")
        print(f"  分类数据目录: {classification_dir}")
        
        dataset_info = trainer.prepare_training_data_from_segmentation(
            segmentation_dir, classification_dir
        )
        
        print("✅ 数据准备成功！")
        print(f"\n📊 数据集信息:")
        print(f"  类别数: {dataset_info['num_classes']}")
        print(f"  总样本数: {dataset_info['total_samples']}")
        print(f"  类别: {dataset_info['classes']}")
        
        for class_name, count in dataset_info['class_counts'].items():
            print(f"    {class_name}: {count} 个样本")
        
        # 验证输出目录结构
        classification_path = Path(classification_dir)
        if classification_path.exists():
            print(f"\n📂 输出目录结构:")
            for class_dir in classification_path.iterdir():
                if class_dir.is_dir():
                    image_files = list(class_dir.glob("*.jpg"))
                    print(f"  {class_dir.name}/: {len(image_files)} 个图像文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_folder_structure_detection():
    """测试文件夹结构检测"""
    print("\n🔍 测试文件夹结构检测...")
    
    try:
        from classification_trainer import ClassificationTrainer
        from classification_manager import ClassificationManager
        
        manager = ClassificationManager()
        trainer = ClassificationTrainer(manager)
        
        # 创建测试数据
        test_dir = create_mock_segmentation_output()
        
        # 测试_find_segmentation_results方法
        results = trainer._find_segmentation_results(test_dir)
        
        print(f"✅ 找到 {len(results)} 个分割结果")
        
        for i, result in enumerate(results):
            if len(result) == 3:
                data, image, class_name = result
                if isinstance(data, list):
                    print(f"  结果 {i+1}: 类别='{class_name}', 种子图像={len(data)}个")
                else:
                    print(f"  结果 {i+1}: 类别='{class_name}', JSON文件={data}")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ 文件夹结构检测失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    import shutil
    
    test_dirs = ["test_segmentation_output", "test_classification_data"]
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"✅ 删除测试目录: {test_dir}")

def main():
    print("🌱 分类数据准备修复测试")
    print("=" * 50)
    
    tests = [
        ("文件夹结构检测", test_folder_structure_detection),
        ("分类数据准备", test_classification_data_preparation),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n📊 结果: {passed}/{len(tests)} 项测试通过")
    
    if passed == len(tests):
        print("\n🎉 分类数据准备修复成功！")
        print("\n✅ 修复内容:")
        print("- 支持从文件夹结构直接提取种子图像")
        print("- 自动识别种子图像文件")
        print("- 排除特殊文件（transparent_segmentation等）")
        print("- 使用文件夹名称作为类别标签")
        print("- 兼容原有的JSON格式分割结果")
        
        print("\n📋 使用方法:")
        print("1. 分割结果目录: 选择包含各类别文件夹的目录")
        print("2. 分类数据目录: 设置输出目录")
        print("3. 点击'从分割结果准备分类数据'")
        print("4. 系统会自动:")
        print("   - 扫描各个类别文件夹")
        print("   - 提取种子图像文件")
        print("   - 按类别组织数据")
        print("   - 生成训练数据集")
    else:
        print("\n⚠️ 部分功能需要进一步调试")
    
    # 清理测试数据
    cleanup_test_data()
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
