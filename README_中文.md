# 🌱 种子分析系统

基于深度学习的综合种子检测、分类和分析系统，适用于农业研究、质量控制和种子分拣应用。

## 📋 项目概述

本项目提供端到端的种子分析解决方案，结合图像分割、目标检测和分类技术，自动识别和分类数字图像中的种子。系统专为农业研究、质量控制和种子分拣应用而设计。

## ✨ 主要功能

### 🔍 图像分割
- **SAM分割**: 利用Segment Anything Model进行精确的种子边界检测
- **掩膜分割**: 基于传统计算机视觉技术的预处理掩膜分割
- **透明背景分割**: 生成透明背景的干净种子图像
- **自动YOLO标注**: 为目标检测模型创建训练数据

### 🎯 YOLO目标检测
- **专业种子检测**: 针对种子检测任务优化的自定义YOLO训练
- **多模型支持**: YOLOv8n、YOLOv8s、YOLOv8l，提供不同的速度/精度权衡
- **优化训练参数**: 专门针对种子的数据增强和训练策略
- **实时检测**: 快速推理，支持批处理和实时应用

### 🧠 分类训练
- **多种架构**: 支持ResNet、EfficientNet、MobileNet、DenseNet、VGG
- **迁移学习**: 预训练模型实现更快收敛和更好性能
- **自动数据准备**: 将分割结果转换为分类训练数据
- **基于类别的组织**: 使用文件夹名称作为类别标签，便于数据管理

### 🖥️ 用户界面
- **统一GUI**: 集成所有功能的综合界面
- **实时进度**: 实时训练进度和日志记录
- **可视化结果**: 图像预览和结果可视化
- **批处理**: 高效处理多个图像和数据集

## 🛠️ 安装说明

### 系统要求
- Python 3.8+
- CUDA 11.2+ (GPU加速)
- 8GB+ 内存 (推荐16GB+)
- 4GB+ 显存的GPU (用于训练)

### 依赖安装

1. **克隆仓库**:
```bash
git clone <repository-url>
cd zhongzi
```

2. **安装PyTorch (CUDA支持)**:
```bash
pip install torch==1.12.1+cu112 torchvision==0.13.1+cu112 torchaudio==0.12.1+cu112 --extra-index-url https://download.pytorch.org/whl/cu112
```

3. **安装其他依赖**:
```bash
pip install -r requirements.txt
```

4. **下载SAM模型权重**:
```bash
# SAM模型权重文件 (sam_vit_h_4b8939.pth) 应放置在项目根目录
# 下载地址: https://github.com/facebookresearch/segment-anything
```

### 高级功能的额外依赖

YOLO训练:
```bash
pip install ultralytics
```

高级分类模型:
```bash
pip install timm  # 用于EfficientNet等模型
```

## 🚀 快速开始

### 1. 启动GUI界面
```bash
python enhanced_segmentation_gui.py
```

### 2. 基本工作流程
1. **准备图像**: 按类别将种子图像组织到不同文件夹中
2. **图像分割**: 使用透明背景分割并启用YOLO标注
3. **YOLO训练**: 创建数据集并训练目标检测模型
4. **分类训练**: 准备分类数据并训练分类模型
5. **推理**: 使用训练好的模型进行种子检测和分类

## 📖 详细使用指南

### 图像分割

#### 透明背景分割 (推荐)
1. 选择"图像分割"标签页
2. 选择"透明背景分割"
3. ✅ 启用"生成YOLO标注文件"
4. 选择包含按类别组织的种子图像的输入目录:
   ```
   input_images/
   ├── wheat/    (小麦)
   ├── corn/     (玉米)
   ├── rice/     (水稻)
   └── soybean/  (大豆)
   ```
5. 设置输出目录并开始处理

#### 输出结构
```
output/
├── wheat/
│   ├── seed_1.jpg              # 单个种子图像
│   ├── seed_2.jpg
│   ├── transparent_segmentation.png  # 完整分割结果
│   └── yolo_annotations.json   # YOLO训练数据
├── corn/
└── ...
```

### YOLO目标检测训练

#### 1. 创建YOLO数据集
1. 切换到"YOLO训练与目标检测"标签页
2. 选择"YOLO训练"子标签页
3. 点击"创建YOLO数据集"
4. ✅ 选择"种子检测专用优化模式"

#### 2. 训练YOLO模型
1. ✅ 启用"🌱 种子检测专用模式"
2. 选择基础模型 (推荐: yolov8n 快速训练)
3. 配置训练参数:
   - 训练轮数: 50
   - 批次大小: 8
   - 图像尺寸: 640
4. 开始训练

#### 训练特性
- **专业参数**: 针对种子检测任务优化
- **数据增强**: 种子专用的增强策略
- **早停机制**: 通过耐心机制防止过拟合
- **进度监控**: 实时训练日志和指标

### 分类训练

#### 1. 准备分类数据
1. 切换到"分类训练"标签页
2. 选择分割结果目录
3. 设置分类数据输出目录
4. 点击"从分割结果准备分类数据"

#### 2. 训练分类模型
1. 选择模型架构 (ResNet18、EfficientNet-B0等)
2. 配置训练参数:
   - 学习率: 0.001
   - 批次大小: 32
   - 训练轮数: 50
3. 开始训练

#### 支持的模型
- **ResNet**: ResNet18, ResNet34, ResNet50, ResNet101
- **EfficientNet**: EfficientNet-B0 到 B4
- **MobileNet**: MobileNetV2, MobileNetV3
- **DenseNet**: DenseNet121, DenseNet169
- **VGG**: VGG16, VGG19

### 分类识别

#### 检测+分类组合
1. 切换到"分类识别"标签页
2. 加载训练好的YOLO检测模型
3. 加载训练好的分类模型
4. 选择测试图像
5. 运行推理获得带类别标签的检测框

## 📁 项目结构

```
zhongzi/
├── README_中文.md                     # 本文件
├── requirements.txt                   # Python依赖
├── enhanced_segmentation_gui.py       # 主GUI应用程序
├── sam_vit_h_4b8939.pth              # SAM模型权重
│
├── 核心模块/
│   ├── sam_everything_gpu.py          # SAM分割
│   ├── mask_based_segmentation.py     # 掩膜分割
│   ├── transparent_seed_segmentation.py # 透明背景分割
│   ├── yolo_manager.py                # YOLO训练和推理
│   ├── classification_manager.py      # 分类训练
│   └── classification_recognizer.py   # 分类推理
│
├── GUI组件/
│   ├── yolo_gui_components.py         # YOLO界面组件
│   └── classification_gui_components.py # 分类界面组件
│
├── 数据目录/
│   ├── output/                        # 分割结果
│   ├── classification_data/           # 分类训练数据
│   ├── yolo_models/                   # 训练好的YOLO模型
│   ├── classification_models/         # 训练好的分类模型
│   └── sample_classification_data/    # 测试样本数据
│
├── 文档/
│   ├── 完整种子分析系统使用指南.md      # 完整系统指南
│   ├── 种子检测YOLO训练指南.md         # YOLO训练指南
│   └── 分类训练数据准备指南.md         # 分类数据指南
│
└── 工具/
    ├── yolo_format_converter.py       # 数据格式转换
    ├── seed_detection_optimizer.py    # 检测优化
    └── 各种测试和演示文件
```

## 🔄 完整工作流程

### 阶段1: 数据准备
1. **组织图像**: 将种子图像按类别放入特定文件夹
2. **图像分割**: 提取带透明背景的单个种子
3. **生成标注**: 自动创建YOLO格式标注

### 阶段2: 模型训练
1. **YOLO训练**: 训练种子定位的目标检测模型
2. **分类训练**: 使用裁剪的种子图像训练分类模型
3. **模型验证**: 在验证集上评估模型性能

### 阶段3: 推理和分析
1. **种子检测**: 使用训练好的YOLO模型定位新图像中的种子
2. **种子分类**: 使用训练好的分类模型对检测到的种子进行分类
3. **结果分析**: 生成包含检测和分类结果的报告

## 🎯 性能预期

### YOLO检测
- **准确率**: 在良好准备的数据集上mAP@0.5 > 0.85
- **速度**: RTX 4090上约50-100 FPS (取决于模型大小)
- **训练时间**: RTX 4090上50轮训练15-30分钟

### 分类
- **准确率**: 在平衡数据集上>90% (样本充足)
- **训练时间**: RTX 4090上50轮训练10-20分钟
- **推理速度**: GPU上每个种子<1ms

## 🔧 配置和自定义

### 分割参数
- 透明度检测的Alpha阈值
- 最小/最大种子面积过滤器
- 检测种子周围的填充
- 噪声去除选项

### YOLO训练参数
- 种子专用数据增强
- 优化的学习率调度
- 早停机制
- 模型架构选择

### 分类参数
- 预训练模型的迁移学习
- 数据增强策略
- 学习率和优化设置
- 模型架构选择

## 🐛 故障排除

### 常见问题

#### "CUDA内存不足"
- 减少批次大小
- 使用较小的模型 (如yolov8n而不是yolov8l)
- 关闭其他GPU应用程序

#### "未找到分割结果"
- 确保分割过程中启用了YOLO标注
- 检查输出目录是否包含类别文件夹
- 验证支持的图像文件格式

#### "训练不收敛"
- 检查数据集质量和平衡性
- 调整学习率
- 增加训练轮数
- 验证标注准确性

### 性能优化
- 对所有训练使用GPU加速
- 针对您的硬件优化批次大小
- 训练期间监控内存使用
- 根据用例使用适当的模型大小

## 📊 数据要求

### 最低要求
- **每类10+样本** 基本功能
- **平衡数据集** 每类样本数量相似
- **清晰高质量图像** 良好光照条件

### 推荐配置
- **每类50-200样本** 获得最佳性能
- **224x224+像素分辨率** 用于分类
- **多种光照条件** 提高鲁棒性
- **多样化背景** 增强泛化能力

## 📈 高级功能

### 批处理
- 同时处理多个图像
- 自动进度跟踪和日志记录
- 恢复中断的处理会话
- 并行处理提高吞吐量

### 模型管理
- 自动模型下载和缓存
- 训练模型的版本控制
- 模型性能比较工具
- 导出模型用于部署

### 数据增强
- 种子图像专用增强
- 可配置的增强管道
- 实时增强预览
- 自定义增强策略

### 集成能力
- 自动化的命令行界面
- Web集成的API端点
- 多格式结果导出 (JSON, CSV, XML)
- 与农业数据库集成

## 🔬 技术细节

### 分割算法
- **SAM (Segment Anything)**: 最先进的分割基础模型
- **透明背景**: 干净种子提取的自定义算法
- **基于掩膜**: 传统计算机视觉与形态学操作
- **混合方法**: 结合多种方法获得最佳结果

### 种子YOLO优化
- **单类检测**: 针对二元种子/非种子分类优化
- **小目标检测**: 增强的锚点策略用于小种子
- **数据增强**: 种子特定的增强策略
- **迁移学习**: 在COCO上预训练，针对种子微调

### 分类架构
- **迁移学习**: 利用ImageNet预训练模型
- **多尺度特征**: 捕获种子的精细和粗糙特征
- **集成方法**: 结合多个模型提高准确性
- **注意力机制**: 专注于判别性种子特征

## 🌐 使用案例和应用

### 农业研究
- **表型研究**: 自动化种子性状测量
- **遗传分析**: 大规模种子特征分析
- **育种项目**: 定量种子评估
- **质量评估**: 自动化种子质量分级

### 工业应用
- **种子分拣**: 加工厂的自动化分类
- **质量控制**: 实时种子检测系统
- **库存管理**: 自动化种子计数和分类
- **包装验证**: 确保包装中种子类型正确

### 教育和研究
- **教学工具**: 演示计算机视觉概念
- **研究平台**: 种子分析研究的基线
- **基准测试**: 比较不同方法和算法
- **数据收集**: 为研究生成标记数据集

## 📊 性能基准

### 不同用例的硬件要求

#### 开发和测试
- **CPU**: Intel i5 或 AMD Ryzen 5
- **内存**: 8GB最低，推荐16GB
- **GPU**: GTX 1060或更好 (可选但推荐)
- **存储**: 10GB可用空间

#### 生产训练
- **CPU**: Intel i7/i9 或 AMD Ryzen 7/9
- **内存**: 16GB最低，推荐32GB
- **GPU**: RTX 3070或更好 (8GB+显存)
- **存储**: 50GB+可用空间用于数据集

#### 高吞吐量处理
- **CPU**: Intel Xeon 或 AMD EPYC
- **内存**: 32GB+
- **GPU**: RTX 4090 或 A100 (24GB+显存)
- **存储**: NVMe SSD用于快速数据访问

### 性能指标

#### 分割速度
- **SAM**: 约2-5秒每图像 (GPU)
- **透明**: 约0.5-1秒每图像
- **基于掩膜**: 约0.1-0.3秒每图像

#### 训练时间 (RTX 4090)
- **YOLO (50轮)**: 15-30分钟
- **分类 (50轮)**: 10-20分钟
- **完整管道**: 30-60分钟

#### 推理速度
- **YOLO检测**: 50-100 FPS
- **分类**: 1000+种子/秒
- **端到端**: 20-50图像/秒

## 🔧 高级配置

### 环境变量
```bash
# CUDA设置
export CUDA_VISIBLE_DEVICES=0
export TORCH_CUDA_ARCH_LIST="7.5;8.0;8.6"

# 内存优化
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# 日志记录
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

### 配置文件
- `config/segmentation.yaml`: 分割参数
- `config/yolo_training.yaml`: YOLO训练配置
- `config/classification.yaml`: 分类设置
- `config/inference.yaml`: 推理管道设置

### 自定义模型集成
```python
# 示例: 添加自定义分类模型
from classification_manager import ClassificationManager

class CustomModel(nn.Module):
    # 您的自定义模型实现
    pass

# 在系统中注册
manager = ClassificationManager()
manager.register_custom_model('custom_model', CustomModel)
```

## 🚀 部署选项

### 本地部署
- 带GUI的桌面应用程序
- 自动化的命令行工具
- 集成的Python API

### 云部署
- 可扩展性的Docker容器
- Kubernetes编排
- REST API端点

### 边缘部署
- 边缘设备的ONNX模型导出
- NVIDIA设备的TensorRT优化
- PyTorch Mobile的移动部署

## 🔍 监控和日志

### 训练监控
- 实时损失和准确率图表
- TensorBoard集成
- Weights & Biases支持
- 自定义指标跟踪

### 生产监控
- 处理速度指标
- 错误率跟踪
- 资源利用率监控
- 质量保证检查

### 日志配置
```python
import logging

# 配置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('seed_analysis.log'),
        logging.StreamHandler()
    ]
)
```

## 🧪 测试和验证

### 单元测试
```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试类别
python -m pytest tests/test_segmentation.py
python -m pytest tests/test_yolo.py
python -m pytest tests/test_classification.py
```

### 集成测试
```bash
# 测试完整管道
python test_integration.py

# 使用样本数据测试
python test_sample_data.py
```

### 性能测试
```bash
# 基准处理速度
python benchmark_performance.py

# 内存使用分析
python profile_memory.py
```

## 🙏 致谢

- **Segment Anything Model (SAM)** by Meta AI
- **YOLOv8** by Ultralytics
- **PyTorch** 深度学习生态系统
- **OpenCV** 计算机视觉操作
- **Ultralytics** YOLO实现
- **timm** 分类模型库
- **农业研究社区** 领域专业知识

## 📚 参考文献和延伸阅读

### 学术论文
- "Segment Anything" - Kirillov et al., 2023
- "YOLOv8: A New Real-Time Object Detection Algorithm" - Ultralytics, 2023
- "EfficientNet: Rethinking Model Scaling for Convolutional Neural Networks" - Tan & Le, 2019
- "Deep Residual Learning for Image Recognition" - He et al., 2016

### 文档链接
- [PyTorch文档](https://pytorch.org/docs/)
- [Ultralytics YOLOv8](https://docs.ultralytics.com/)
- [Segment Anything](https://segment-anything.com/)
- [timm文档](https://timm.fast.ai/)

### 社区资源
- [计算机视觉社区](https://www.reddit.com/r/computervision/)
- [农业AI研究](https://www.agricultureai.org/)
- [PyTorch论坛](https://discuss.pytorch.org/)

---

**🌱 祝您种子分析愉快！愿您的模型准确无误，收获满满！**
