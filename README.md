# 🌱 Seed Analysis System

A comprehensive computer vision system for automated seed detection, classification, and analysis using advanced deep learning techniques.

## 📋 Project Overview

This project provides an end-to-end solution for seed analysis, combining image segmentation, object detection, and classification to automatically identify and categorize seeds in digital images. The system is designed for agricultural research, quality control, and seed sorting applications.

## ✨ Key Features

### 🔍 Image Segmentation
- **SAM-based Segmentation**: Leverages Segment Anything Model for precise seed boundary detection
- **Mask-based Segmentation**: Traditional computer vision techniques for pre-processed masks
- **Transparent Background Segmentation**: Generates clean seed images with transparent backgrounds
- **Automatic YOLO Annotation**: Creates training data for object detection models

### 🎯 YOLO Object Detection
- **Specialized Seed Detection**: Custom YOLO training optimized for seed detection tasks
- **Multiple Model Support**: YOLOv8n, YOLOv8s, YOLOv8l with different speed/accuracy trade-offs
- **Optimized Training Parameters**: Specialized data augmentation and training strategies for seeds
- **Real-time Detection**: Fast inference for batch processing and real-time applications

### 🧠 Classification Training
- **Multiple Architectures**: Support for ResNet, EfficientNet, MobileNet, DenseNet, VGG
- **Transfer Learning**: Pre-trained models for faster convergence and better performance
- **Automatic Data Preparation**: Converts segmentation results into classification training data
- **Class-based Organization**: Uses folder names as class labels for intuitive data management

### 🖥️ User Interface
- **Unified GUI**: Comprehensive interface combining all functionality
- **Real-time Progress**: Live training progress and logging
- **Visual Results**: Image preview and result visualization
- **Batch Processing**: Handle multiple images and datasets efficiently

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- CUDA 11.2+ (for GPU acceleration)
- 8GB+ RAM (16GB+ recommended)
- GPU with 4GB+ VRAM (for training)

### Dependencies Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd zhongzi
```

2. **Install PyTorch with CUDA support**:
```bash
pip install torch==1.12.1+cu112 torchvision==0.13.1+cu112 torchaudio==0.12.1+cu112 --extra-index-url https://download.pytorch.org/whl/cu112
```

3. **Install other dependencies**:
```bash
pip install -r requirements.txt
```

4. **Download SAM model weights**:
```bash
# The SAM model weights (sam_vit_h_4b8939.pth) should be placed in the project root
# Download from: https://github.com/facebookresearch/segment-anything
```

### Additional Dependencies for Advanced Features

For YOLO training:
```bash
pip install ultralytics
```

For advanced classification models:
```bash
pip install timm  # For EfficientNet and other models
```

## 🚀 Quick Start

### 1. Launch the GUI
```bash
python enhanced_segmentation_gui.py
```

### 2. Basic Workflow
1. **Prepare Images**: Organize seed images by class in separate folders
2. **Image Segmentation**: Use transparent background segmentation with YOLO annotation enabled
3. **YOLO Training**: Create dataset and train object detection model
4. **Classification Training**: Prepare classification data and train classification model
5. **Inference**: Use trained models for seed detection and classification

## 📖 Detailed Usage Guide

### Image Segmentation

#### Transparent Background Segmentation (Recommended)
1. Select "图像分割" (Image Segmentation) tab
2. Choose "透明背景分割" (Transparent Background Segmentation)
3. ✅ Enable "生成YOLO标注文件" (Generate YOLO Annotation Files)
4. Select input directory containing seed images organized by class:
   ```
   input_images/
   ├── wheat/
   ├── corn/
   ├── rice/
   └── soybean/
   ```
5. Set output directory and start processing

#### Output Structure
```
output/
├── wheat/
│   ├── seed_1.jpg              # Individual seed images
│   ├── seed_2.jpg
│   ├── transparent_segmentation.png  # Full segmentation result
│   └── yolo_annotations.json   # YOLO training data
├── corn/
└── ...
```

### YOLO Object Detection Training

#### 1. Create YOLO Dataset
1. Switch to "YOLO训练与目标检测" (YOLO Training & Object Detection) tab
2. Select "YOLO训练" (YOLO Training) sub-tab
3. Click "创建YOLO数据集" (Create YOLO Dataset)
4. ✅ Select "种子检测专用优化模式" (Seed Detection Optimization Mode)

#### 2. Train YOLO Model
1. ✅ Enable "🌱 种子检测专用模式" (Seed Detection Specialized Mode)
2. Select base model (recommended: yolov8n for fast training)
3. Configure training parameters:
   - Epochs: 50
   - Batch size: 8
   - Image size: 640
4. Start training

#### Training Features
- **Specialized Parameters**: Optimized for seed detection tasks
- **Data Augmentation**: Custom augmentation strategy for seeds
- **Early Stopping**: Prevents overfitting with patience mechanism
- **Progress Monitoring**: Real-time training logs and metrics

### Classification Training

#### 1. Prepare Classification Data
1. Switch to "分类训练" (Classification Training) tab
2. Select segmentation results directory
3. Set classification data output directory
4. Click "从分割结果准备分类数据" (Prepare Classification Data from Segmentation Results)

#### 2. Train Classification Model
1. Select model architecture (ResNet18, EfficientNet-B0, etc.)
2. Configure training parameters:
   - Learning rate: 0.001
   - Batch size: 32
   - Epochs: 50
3. Start training

#### Supported Models
- **ResNet**: ResNet18, ResNet34, ResNet50, ResNet101
- **EfficientNet**: EfficientNet-B0 through B4
- **MobileNet**: MobileNetV2, MobileNetV3
- **DenseNet**: DenseNet121, DenseNet169
- **VGG**: VGG16, VGG19

### Classification Recognition

#### Combined Detection + Classification
1. Switch to "分类识别" (Classification Recognition) tab
2. Load trained YOLO model for detection
3. Load trained classification model
4. Select test images
5. Run inference to get detection boxes with class labels

## 📁 Project Structure

```
zhongzi/
├── README.md                          # This file
├── requirements.txt                   # Python dependencies
├── enhanced_segmentation_gui.py       # Main GUI application
├── sam_vit_h_4b8939.pth              # SAM model weights
│
├── Core Modules/
│   ├── sam_everything_gpu.py          # SAM-based segmentation
│   ├── mask_based_segmentation.py     # Mask-based segmentation
│   ├── transparent_seed_segmentation.py # Transparent background segmentation
│   ├── yolo_manager.py                # YOLO training and inference
│   ├── classification_manager.py      # Classification training
│   └── classification_recognizer.py   # Classification inference
│
├── GUI Components/
│   ├── yolo_gui_components.py         # YOLO interface components
│   └── classification_gui_components.py # Classification interface components
│
├── Data Directories/
│   ├── output/                        # Segmentation results
│   ├── classification_data/           # Classification training data
│   ├── yolo_models/                   # Trained YOLO models
│   ├── classification_models/         # Trained classification models
│   └── sample_classification_data/    # Sample data for testing
│
├── Documentation/
│   ├── 完整种子分析系统使用指南.md      # Complete system guide (Chinese)
│   ├── 种子检测YOLO训练指南.md         # YOLO training guide (Chinese)
│   └── 分类训练数据准备指南.md         # Classification data guide (Chinese)
│
└── Utilities/
    ├── yolo_format_converter.py       # Data format conversion
    ├── seed_detection_optimizer.py    # Detection optimization
    └── various test and demo files
```

## 🔄 Complete Workflow

### Stage 1: Data Preparation
1. **Organize Images**: Place seed images in class-specific folders
2. **Image Segmentation**: Extract individual seeds with transparent backgrounds
3. **Generate Annotations**: Create YOLO format annotations automatically

### Stage 2: Model Training
1. **YOLO Training**: Train object detection model for seed localization
2. **Classification Training**: Train classification model using cropped seed images
3. **Model Validation**: Evaluate model performance on validation sets

### Stage 3: Inference and Analysis
1. **Seed Detection**: Locate seeds in new images using trained YOLO model
2. **Seed Classification**: Classify detected seeds using trained classification model
3. **Results Analysis**: Generate reports with detection and classification results

## 🎯 Performance Expectations

### YOLO Detection
- **Accuracy**: mAP@0.5 > 0.85 for well-prepared datasets
- **Speed**: ~50-100 FPS on RTX 4090 (depending on model size)
- **Training Time**: 15-30 minutes for 50 epochs on RTX 4090

### Classification
- **Accuracy**: >90% on balanced datasets with sufficient samples
- **Training Time**: 10-20 minutes for 50 epochs on RTX 4090
- **Inference Speed**: <1ms per seed on GPU

## 🔧 Configuration and Customization

### Segmentation Parameters
- Alpha threshold for transparency detection
- Minimum/maximum seed area filters
- Padding around detected seeds
- Noise removal options

### YOLO Training Parameters
- Specialized data augmentation for seeds
- Optimized learning rate schedules
- Early stopping mechanisms
- Model architecture selection

### Classification Parameters
- Transfer learning from pre-trained models
- Data augmentation strategies
- Learning rate and optimization settings
- Model architecture selection

## 🐛 Troubleshooting

### Common Issues

#### "CUDA out of memory"
- Reduce batch size
- Use smaller model (e.g., yolov8n instead of yolov8l)
- Close other GPU applications

#### "No segmentation results found"
- Ensure YOLO annotation is enabled during segmentation
- Check that output directory contains class folders
- Verify image file formats are supported

#### "Training not converging"
- Check dataset quality and balance
- Adjust learning rate
- Increase training epochs
- Verify annotation accuracy

### Performance Optimization
- Use GPU acceleration for all training
- Optimize batch sizes for your hardware
- Monitor memory usage during training
- Use appropriate model sizes for your use case

## 📊 Data Requirements

### Minimum Requirements
- **10+ samples per class** for basic functionality
- **Balanced datasets** with similar sample counts per class
- **Clear, high-quality images** with good lighting

### Recommended Configuration
- **50-200 samples per class** for optimal performance
- **224x224+ pixel resolution** for classification
- **Multiple lighting conditions** for robustness
- **Diverse backgrounds** for generalization

## 🤝 Contributing

This project is designed for agricultural research and seed analysis applications. Contributions are welcome for:
- Additional segmentation algorithms
- New classification architectures
- Performance optimizations
- Documentation improvements

## 📄 License

[Add your license information here]

## 📈 Advanced Features

### Batch Processing
- Process multiple images simultaneously
- Automatic progress tracking and logging
- Resume interrupted processing sessions
- Parallel processing for faster throughput

### Model Management
- Automatic model downloading and caching
- Version control for trained models
- Model performance comparison tools
- Export models for deployment

### Data Augmentation
- Specialized augmentation for seed images
- Configurable augmentation pipelines
- Real-time augmentation preview
- Custom augmentation strategies

### Integration Capabilities
- Command-line interface for automation
- API endpoints for web integration
- Export results in multiple formats (JSON, CSV, XML)
- Integration with agricultural databases

## 🔬 Technical Details

### Segmentation Algorithms
- **SAM (Segment Anything)**: State-of-the-art foundation model for segmentation
- **Transparent Background**: Custom algorithm for clean seed extraction
- **Mask-based**: Traditional computer vision with morphological operations
- **Hybrid Approach**: Combines multiple methods for optimal results

### YOLO Optimization for Seeds
- **Single-class Detection**: Optimized for binary seed/non-seed classification
- **Small Object Detection**: Enhanced anchor strategies for small seeds
- **Data Augmentation**: Seed-specific augmentation policies
- **Transfer Learning**: Pre-trained on COCO, fine-tuned for seeds

### Classification Architecture
- **Transfer Learning**: Leverages ImageNet pre-trained models
- **Multi-scale Features**: Captures both fine and coarse seed characteristics
- **Ensemble Methods**: Combines multiple models for improved accuracy
- **Attention Mechanisms**: Focus on discriminative seed features

## 🌐 Use Cases and Applications

### Agricultural Research
- **Phenotyping Studies**: Automated seed trait measurement
- **Genetic Analysis**: Large-scale seed characteristic analysis
- **Breeding Programs**: Quantitative seed evaluation
- **Quality Assessment**: Automated seed quality grading

### Industrial Applications
- **Seed Sorting**: Automated classification for processing plants
- **Quality Control**: Real-time seed inspection systems
- **Inventory Management**: Automated seed counting and categorization
- **Packaging Verification**: Ensure correct seed types in packages

### Educational and Research
- **Teaching Tool**: Demonstrate computer vision concepts
- **Research Platform**: Baseline for seed analysis research
- **Benchmarking**: Compare different approaches and algorithms
- **Data Collection**: Generate labeled datasets for research

## 📊 Performance Benchmarks

### Hardware Requirements by Use Case

#### Development and Testing
- **CPU**: Intel i5 or AMD Ryzen 5
- **RAM**: 8GB minimum, 16GB recommended
- **GPU**: GTX 1060 or better (optional but recommended)
- **Storage**: 10GB free space

#### Production Training
- **CPU**: Intel i7/i9 or AMD Ryzen 7/9
- **RAM**: 16GB minimum, 32GB recommended
- **GPU**: RTX 3070 or better (8GB+ VRAM)
- **Storage**: 50GB+ free space for datasets

#### High-throughput Processing
- **CPU**: Intel Xeon or AMD EPYC
- **RAM**: 32GB+
- **GPU**: RTX 4090 or A100 (24GB+ VRAM)
- **Storage**: NVMe SSD for fast data access

### Performance Metrics

#### Segmentation Speed
- **SAM**: ~2-5 seconds per image (GPU)
- **Transparent**: ~0.5-1 second per image
- **Mask-based**: ~0.1-0.3 seconds per image

#### Training Times (RTX 4090)
- **YOLO (50 epochs)**: 15-30 minutes
- **Classification (50 epochs)**: 10-20 minutes
- **Full pipeline**: 30-60 minutes

#### Inference Speed
- **YOLO Detection**: 50-100 FPS
- **Classification**: 1000+ seeds/second
- **End-to-end**: 20-50 images/second

## 🔧 Advanced Configuration

### Environment Variables
```bash
# CUDA settings
export CUDA_VISIBLE_DEVICES=0
export TORCH_CUDA_ARCH_LIST="7.5;8.0;8.6"

# Memory optimization
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Logging
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

### Configuration Files
- `config/segmentation.yaml`: Segmentation parameters
- `config/yolo_training.yaml`: YOLO training configuration
- `config/classification.yaml`: Classification settings
- `config/inference.yaml`: Inference pipeline settings

### Custom Model Integration
```python
# Example: Adding a custom classification model
from classification_manager import ClassificationManager

class CustomModel(nn.Module):
    # Your custom model implementation
    pass

# Register with the system
manager = ClassificationManager()
manager.register_custom_model('custom_model', CustomModel)
```

## 🚀 Deployment Options

### Local Deployment
- Desktop application with GUI
- Command-line tools for automation
- Python API for integration

### Cloud Deployment
- Docker containers for scalability
- Kubernetes orchestration
- REST API endpoints

### Edge Deployment
- ONNX model export for edge devices
- TensorRT optimization for NVIDIA devices
- Mobile deployment with PyTorch Mobile

## 🔍 Monitoring and Logging

### Training Monitoring
- Real-time loss and accuracy plots
- TensorBoard integration
- Weights & Biases support
- Custom metric tracking

### Production Monitoring
- Processing speed metrics
- Error rate tracking
- Resource utilization monitoring
- Quality assurance checks

### Logging Configuration
```python
import logging

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('seed_analysis.log'),
        logging.StreamHandler()
    ]
)
```

## 🧪 Testing and Validation

### Unit Tests
```bash
# Run all tests
python -m pytest tests/

# Run specific test categories
python -m pytest tests/test_segmentation.py
python -m pytest tests/test_yolo.py
python -m pytest tests/test_classification.py
```

### Integration Tests
```bash
# Test complete pipeline
python test_integration.py

# Test with sample data
python test_sample_data.py
```

### Performance Tests
```bash
# Benchmark processing speed
python benchmark_performance.py

# Memory usage analysis
python profile_memory.py
```

## 🔄 Continuous Integration

### GitHub Actions Workflow
```yaml
name: Seed Analysis CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    - name: Install dependencies
      run: pip install -r requirements.txt
    - name: Run tests
      run: python -m pytest
```

## 🙏 Acknowledgments

- **Segment Anything Model (SAM)** by Meta AI
- **YOLOv8** by Ultralytics
- **PyTorch** ecosystem for deep learning
- **OpenCV** for computer vision operations
- **Ultralytics** for YOLO implementation
- **timm** library for classification models
- **Agricultural research community** for domain expertise

## 📚 References and Further Reading

### Academic Papers
- "Segment Anything" - Kirillov et al., 2023
- "YOLOv8: A New Real-Time Object Detection Algorithm" - Ultralytics, 2023
- "EfficientNet: Rethinking Model Scaling for Convolutional Neural Networks" - Tan & Le, 2019
- "Deep Residual Learning for Image Recognition" - He et al., 2016

### Documentation Links
- [PyTorch Documentation](https://pytorch.org/docs/)
- [Ultralytics YOLOv8](https://docs.ultralytics.com/)
- [Segment Anything](https://segment-anything.com/)
- [timm Documentation](https://timm.fast.ai/)

### Community Resources
- [Computer Vision Community](https://www.reddit.com/r/computervision/)
- [Agricultural AI Research](https://www.agricultureai.org/)
- [PyTorch Forums](https://discuss.pytorch.org/)

---

**🌱 Happy seed analyzing! May your models be accurate and your harvests bountiful!**
