# 🌱 分类训练数据准备指南

## 问题解决

### 原始问题
```
[13:38:05] 开始准备分类数据...
[13:38:05] 数据准备失败: 未找到分割结果
```

### 问题原因
分类训练器原本只支持特定格式的JSON分割结果文件（`*_segmentation_results.json`），但你的分割输出使用了不同的文件结构。

### 解决方案
修改分类训练器，使其能够：
1. **直接从文件夹结构提取种子图像**
2. **使用文件夹名称作为类别标签**
3. **自动识别和过滤种子图像文件**
4. **兼容原有的JSON格式**

## 🔧 修复内容

### 1. 智能文件检测
```python
# 新增方法：从文件夹结构直接提取
def _extract_from_folder_structure(self, output_dir: str):
    # 遍历所有子文件夹
    # 使用文件夹名称作为类别
    # 自动识别种子图像文件
    # 排除特殊文件
```

### 2. 文件过滤规则
**包含的文件**：
- `.jpg`, `.jpeg`, `.png`, `.bmp`, `.tiff` 图像文件
- 不包含特殊关键词的文件

**排除的文件**：
- `transparent_segmentation.*` - 透明背景分割结果
- `marked.*` - 标记图像
- `original.*` - 原始图像
- `mask.*` - 掩膜文件
- `marked_crops/` 文件夹中的文件

### 3. 双重支持
- **方法1**: 查找JSON分割结果文件（原有格式）
- **方法2**: 直接从文件夹结构提取（新增功能）

## 📁 支持的文件夹结构

### 你的分割输出结构
```
output/
├── wheat/                    # 小麦类别
│   ├── seed_1.jpg           # ✅ 种子图像（会被使用）
│   ├── seed_2.jpg           # ✅ 种子图像（会被使用）
│   ├── seed_3.jpg           # ✅ 种子图像（会被使用）
│   ├── transparent_segmentation.png  # ❌ 排除
│   ├── marked_crops/        # ❌ 排除整个文件夹
│   └── some_result.json     # ❌ 排除
├── corn/                     # 玉米类别
│   ├── seed_1.jpg           # ✅ 种子图像
│   ├── seed_2.jpg           # ✅ 种子图像
│   └── ...
└── rice/                     # 水稻类别
    ├── seed_1.jpg           # ✅ 种子图像
    ├── seed_2.jpg           # ✅ 种子图像
    └── ...
```

### 生成的分类数据结构
```
classification_data/
├── wheat/                    # 小麦分类数据
│   ├── wheat_seed_1_seed_1.jpg
│   ├── wheat_seed_2_seed_2.jpg
│   └── wheat_seed_3_seed_3.jpg
├── corn/                     # 玉米分类数据
│   ├── corn_seed_1_seed_1.jpg
│   ├── corn_seed_2_seed_2.jpg
│   └── ...
├── rice/                     # 水稻分类数据
│   ├── rice_seed_1_seed_1.jpg
│   ├── rice_seed_2_seed_2.jpg
│   └── ...
└── dataset_info.json        # 数据集信息
```

## 🚀 使用方法

### 1. 启动GUI
```bash
python enhanced_segmentation_gui.py
```

### 2. 切换到分类训练标签页
- 点击 **"分类训练"** 标签页

### 3. 设置数据路径
- **分割结果目录**: 选择你的分割输出目录（如 `output/`）
- **分类数据目录**: 设置分类数据输出目录（如 `classification_data/`）

### 4. 准备分类数据
- 点击 **"从分割结果准备分类数据"** 按钮
- 系统会自动：
  - 扫描各个类别文件夹
  - 识别种子图像文件
  - 复制到分类数据目录
  - 按类别组织数据

### 5. 查看结果
控制台会显示：
```
[时间] 在文件夹 'wheat' 中找到 3 个种子图像
[时间] 为类别 'wheat' 复制了 3 个种子图像
[时间] 在文件夹 'corn' 中找到 2 个种子图像
[时间] 为类别 'corn' 复制了 2 个种子图像
[时间] 分类数据准备完成！
[时间] 类别数: 3
[时间] 总样本数: 8
[时间]   wheat: 3 个样本
[时间]   corn: 2 个样本
[时间]   rice: 3 个样本
```

### 6. 开始训练
- 选择分类模型（如 ResNet50）
- 设置训练参数
- 点击 **"开始训练"**

## 📊 数据要求

### 最低要求
- **每个类别至少10个样本** - 用于有效训练
- **类别平衡** - 各类别样本数量尽量接近
- **图像质量** - 清晰的种子图像

### 推荐配置
- **每个类别50-200个样本** - 获得更好的性能
- **图像尺寸** - 224x224像素或更大
- **文件格式** - JPG或PNG格式

## 🔍 故障排除

### 问题1: "未找到分割结果"
**解决方案**:
- 确保分割结果目录包含子文件夹
- 每个子文件夹代表一个种子类别
- 子文件夹中包含种子图像文件

### 问题2: "数据准备完成但样本数为0"
**可能原因**:
- 图像文件被过滤规则排除
- 文件扩展名不被支持
- 文件名包含排除关键词

**解决方案**:
- 检查图像文件扩展名
- 重命名包含特殊关键词的文件
- 查看控制台日志了解详情

### 问题3: "类别数量不正确"
**解决方案**:
- 确保每个类别都有独立的文件夹
- 文件夹名称就是类别名称
- 移除空文件夹

## 🎯 最佳实践

### 1. 数据组织
- 使用有意义的文件夹名称作为类别名
- 保持文件夹结构简洁清晰
- 避免在种子图像文件名中使用特殊关键词

### 2. 数据质量
- 确保种子图像清晰可见
- 移除模糊或损坏的图像
- 保持各类别样本数量平衡

### 3. 训练准备
- 先准备数据，再选择模型
- 根据数据量选择合适的模型大小
- 设置合理的训练参数

## 🎉 修复完成

现在分类训练器可以：
- ✅ **直接处理你的分割输出结构**
- ✅ **自动识别种子图像文件**
- ✅ **使用文件夹名称作为类别**
- ✅ **排除不相关的文件**
- ✅ **生成标准的分类训练数据**

**可以正常进行分类训练了！** 🌱
