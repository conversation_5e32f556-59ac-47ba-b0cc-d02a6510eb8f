#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类训练模块
基于分割结果进行种子分类训练
"""

import os
import json
import shutil
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from pathlib import Path
from typing import Dict, List, Optional, Callable
import logging
from PIL import Image
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt

from classification_manager import ClassificationManager, SeedDataset

class ClassificationTrainer:
    """分类训练器"""
    
    def __init__(self, manager: ClassificationManager):
        self.manager = manager
        self.logger = logging.getLogger(__name__)
        
        # 训练配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger.info(f"使用设备: {self.device}")
    
    def prepare_training_data_from_segmentation(self, segmentation_output_dir: str, 
                                              classification_data_dir: str) -> Dict:
        """
        从分割结果准备分类训练数据
        
        Args:
            segmentation_output_dir: 分割结果目录
            classification_data_dir: 分类数据输出目录
            
        Returns:
            数据准备结果
        """
        self.logger.info("从分割结果准备分类训练数据")
        
        # 创建分类数据目录
        classification_data_path = Path(classification_data_dir)
        classification_data_path.mkdir(exist_ok=True)
        
        # 扫描分割结果
        segmentation_results = self._find_segmentation_results(segmentation_output_dir)
        
        if not segmentation_results:
            raise ValueError("未找到分割结果")
        
        # 按文件夹名称（种子类别）组织数据
        class_stats = {}
        total_seeds = 0

        for result_data in segmentation_results:
            if len(result_data) == 3:
                result_file, original_image, folder_name = result_data

                # 创建类别目录
                class_dir = classification_data_path / folder_name
                class_dir.mkdir(exist_ok=True)

                # 判断是JSON格式还是直接的图像列表
                if isinstance(result_file, str) and result_file.endswith('.json'):
                    # JSON格式：传统的分割结果
                    seed_count = self._extract_seeds_for_classification(
                        result_file, original_image, class_dir, folder_name
                    )
                elif isinstance(result_file, list):
                    # 图像列表格式：直接复制种子图像
                    seed_count = self._copy_seed_images_for_classification(
                        result_file, class_dir, folder_name
                    )
                else:
                    self.logger.warning(f"未知的结果格式: {result_file}")
                    continue

                if folder_name not in class_stats:
                    class_stats[folder_name] = 0
                class_stats[folder_name] += seed_count
                total_seeds += seed_count
        
        # 保存数据集信息
        dataset_info = {
            'dataset_dir': str(classification_data_path),
            'classes': list(class_stats.keys()),
            'class_counts': class_stats,
            'total_samples': total_seeds,
            'num_classes': len(class_stats),
            'task': 'seed_classification'
        }
        
        info_file = classification_data_path / 'dataset_info.json'
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"分类数据准备完成:")
        self.logger.info(f"  类别数: {len(class_stats)}")
        self.logger.info(f"  总样本数: {total_seeds}")
        for class_name, count in class_stats.items():
            self.logger.info(f"  {class_name}: {count} 个样本")
        
        return dataset_info
    
    def _find_segmentation_results(self, output_dir: str) -> List[tuple]:
        """查找分割结果 - 支持多种格式"""
        results = []

        # 方法1: 查找传统的JSON分割结果文件
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                if file.endswith('_segmentation_results.json'):
                    json_path = os.path.join(root, file)

                    # 获取文件夹名称作为类别
                    folder_name = os.path.basename(root)

                    # 查找对应的原始图像
                    base_name = file.replace('_segmentation_results.json', '')

                    # 查找原始图像文件
                    image_path = None
                    for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                        potential_image = os.path.join(root, base_name + ext)
                        if os.path.exists(potential_image):
                            image_path = potential_image
                            break

                    if image_path:
                        results.append((json_path, image_path, folder_name))

        # 方法2: 如果没找到JSON文件，直接从文件夹结构提取
        if not results:
            self.logger.info("未找到JSON分割结果，尝试从文件夹结构提取种子图像")
            results = self._extract_from_folder_structure(output_dir)

        return results

    def _extract_from_folder_structure(self, output_dir: str) -> List[tuple]:
        """从文件夹结构直接提取种子图像"""
        results = []

        # 遍历输出目录中的所有子文件夹
        for root, dirs, files in os.walk(output_dir):
            folder_name = os.path.basename(root)

            # 跳过根目录和特殊文件夹
            if root == output_dir or folder_name in ['marked_crops', '__pycache__']:
                continue

            # 查找种子图像文件（排除特殊文件）
            seed_images = []
            for file in files:
                file_path = os.path.join(root, file)

                # 检查是否是图像文件
                if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff')):
                    # 排除特殊文件
                    if not any(keyword in file.lower() for keyword in [
                        'transparent_segmentation', 'marked', 'original', 'mask'
                    ]):
                        seed_images.append(file_path)

            # 如果找到种子图像，创建虚拟结果条目
            if seed_images:
                # 使用文件夹名称作为类别名
                class_name = folder_name

                # 创建虚拟的分割结果条目
                # 格式: (种子图像列表, None, 类别名)
                results.append((seed_images, None, class_name))

                self.logger.info(f"在文件夹 '{class_name}' 中找到 {len(seed_images)} 个种子图像")

        return results

    def _extract_seeds_for_classification(self, json_path: str, image_path: str,
                                        class_dir: Path, class_name: str) -> int:
        """从分割结果提取种子用于分类训练"""
        try:
            # 读取分割结果
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 读取原始图像
            image = Image.open(image_path).convert('RGB')
            
            # 提取每个种子
            seed_count = 0
            for seed in data.get('seeds', []):
                bbox = seed.get('bbox')
                if bbox:
                    x, y, w, h = bbox
                    
                    # 裁剪种子区域
                    seed_image = image.crop((x, y, x + w, y + h))
                    
                    # 保存种子图像
                    base_name = os.path.splitext(os.path.basename(image_path))[0]
                    seed_filename = f"{base_name}_seed_{seed_count + 1}.jpg"
                    seed_path = class_dir / seed_filename
                    
                    seed_image.save(seed_path, 'JPEG', quality=95)
                    seed_count += 1
            
            return seed_count
            
        except Exception as e:
            self.logger.error(f"提取种子失败 {json_path}: {e}")
            return 0

    def _copy_seed_images_for_classification(self, seed_images: List[str],
                                           class_dir: Path, class_name: str) -> int:
        """直接复制种子图像用于分类训练"""
        try:
            seed_count = 0

            for seed_image_path in seed_images:
                if os.path.exists(seed_image_path):
                    # 生成新的文件名
                    original_name = os.path.basename(seed_image_path)
                    name_without_ext = os.path.splitext(original_name)[0]
                    ext = os.path.splitext(original_name)[1]

                    # 创建新的文件名，避免冲突
                    new_filename = f"{class_name}_{name_without_ext}_seed_{seed_count + 1}{ext}"
                    new_path = class_dir / new_filename

                    # 复制文件
                    shutil.copy2(seed_image_path, new_path)
                    seed_count += 1

                    self.logger.debug(f"复制种子图像: {seed_image_path} -> {new_path}")

            self.logger.info(f"为类别 '{class_name}' 复制了 {seed_count} 个种子图像")
            return seed_count

        except Exception as e:
            self.logger.error(f"复制种子图像失败 {class_name}: {e}")
            return 0

    def train_model(self, model_name: str, dataset_dir: str,
                   epochs: int = 50, learning_rate: float = 0.001,
                   batch_size: int = 32, train_ratio: float = 0.8,
                   progress_callback: Optional[Callable] = None,
                   log_callback: Optional[Callable] = None) -> Optional[str]:
        """
        训练分类模型
        
        Args:
            model_name: 模型名称
            dataset_dir: 数据集目录
            epochs: 训练轮数
            learning_rate: 学习率
            batch_size: 批次大小
            train_ratio: 训练集比例
            progress_callback: 进度回调
            log_callback: 日志回调
            
        Returns:
            训练完成的模型路径
        """
        try:
            # 准备数据集
            self.logger.info("准备数据集...")
            if log_callback:
                log_callback("准备数据集...")
            
            # 创建数据集
            full_dataset = SeedDataset(dataset_dir)
            num_classes = len(full_dataset.classes)
            
            if num_classes == 0:
                raise ValueError("数据集为空")
            
            self.logger.info(f"数据集类别数: {num_classes}")
            self.logger.info(f"类别: {full_dataset.classes}")
            
            # 分割数据集
            train_size = int(len(full_dataset) * train_ratio)
            val_size = len(full_dataset) - train_size
            
            train_dataset, val_dataset = torch.utils.data.random_split(
                full_dataset, [train_size, val_size]
            )
            
            # 应用变换
            train_dataset.dataset.transform = self.manager.train_transform
            val_dataset.dataset.transform = self.manager.val_transform
            
            # 创建数据加载器
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
            
            # 创建模型
            self.logger.info(f"创建模型: {model_name}")
            if log_callback:
                log_callback(f"创建模型: {model_name}")
            
            model = self.manager.create_model(model_name, num_classes, pretrained=True)
            model = model.to(self.device)
            
            # 定义损失函数和优化器
            criterion = nn.CrossEntropyLoss()
            optimizer = optim.Adam(model.parameters(), lr=learning_rate)
            scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.1)
            
            # 训练历史
            train_losses = []
            val_losses = []
            train_accs = []
            val_accs = []
            
            best_val_acc = 0.0
            best_model_state = None
            
            # 训练循环
            for epoch in range(epochs):
                # 训练阶段
                model.train()
                train_loss = 0.0
                train_correct = 0
                train_total = 0
                
                for batch_idx, (data, target) in enumerate(train_loader):
                    data, target = data.to(self.device), target.to(self.device)
                    
                    optimizer.zero_grad()
                    output = model(data)
                    loss = criterion(output, target)
                    loss.backward()
                    optimizer.step()
                    
                    train_loss += loss.item()
                    _, predicted = torch.max(output.data, 1)
                    train_total += target.size(0)
                    train_correct += (predicted == target).sum().item()
                
                # 验证阶段
                model.eval()
                val_loss = 0.0
                val_correct = 0
                val_total = 0
                
                with torch.no_grad():
                    for data, target in val_loader:
                        data, target = data.to(self.device), target.to(self.device)
                        output = model(data)
                        loss = criterion(output, target)
                        
                        val_loss += loss.item()
                        _, predicted = torch.max(output.data, 1)
                        val_total += target.size(0)
                        val_correct += (predicted == target).sum().item()
                
                # 计算平均损失和准确率
                avg_train_loss = train_loss / len(train_loader)
                avg_val_loss = val_loss / len(val_loader)
                train_acc = 100.0 * train_correct / train_total
                val_acc = 100.0 * val_correct / val_total
                
                train_losses.append(avg_train_loss)
                val_losses.append(avg_val_loss)
                train_accs.append(train_acc)
                val_accs.append(val_acc)
                
                # 保存最佳模型
                if val_acc > best_val_acc:
                    best_val_acc = val_acc
                    best_model_state = model.state_dict().copy()
                
                # 更新学习率
                scheduler.step()
                
                # 日志输出
                log_msg = f"Epoch [{epoch+1}/{epochs}] - Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc:.2f}%, Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc:.2f}%"
                self.logger.info(log_msg)
                if log_callback:
                    log_callback(log_msg)
                
                # 进度回调
                if progress_callback:
                    progress_callback((epoch + 1) / epochs * 100)
            
            # 保存最佳模型
            if best_model_state is not None:
                model.load_state_dict(best_model_state)
            
            # 保存训练完成的模型
            timestamp = int(time.time())
            model_filename = f"{model_name}_trained_{timestamp}.pth"
            model_path = self.manager.models_dir / model_filename
            
            # 保存模型和相关信息
            torch.save({
                'model_state_dict': model.state_dict(),
                'model_name': model_name,
                'num_classes': num_classes,
                'classes': full_dataset.classes,
                'best_val_acc': best_val_acc,
                'train_history': {
                    'train_losses': train_losses,
                    'val_losses': val_losses,
                    'train_accs': train_accs,
                    'val_accs': val_accs
                }
            }, model_path)
            
            self.logger.info(f"模型训练完成，最佳验证准确率: {best_val_acc:.2f}%")
            self.logger.info(f"模型已保存: {model_path}")
            
            if log_callback:
                log_callback(f"训练完成！最佳验证准确率: {best_val_acc:.2f}%")
                log_callback(f"模型已保存: {model_filename}")
            
            return str(model_path)
            
        except Exception as e:
            self.logger.error(f"训练失败: {e}")
            if log_callback:
                log_callback(f"训练失败: {e}")
            return None

def main():
    """测试函数"""
    import time
    
    manager = ClassificationManager()
    trainer = ClassificationTrainer(manager)
    
    print("分类训练器测试")
    print("=" * 50)
    
    # 测试数据准备
    try:
        # 这里需要实际的分割结果目录
        segmentation_dir = "output"  # 替换为实际路径
        classification_dir = "classification_data"
        
        if os.path.exists(segmentation_dir):
            dataset_info = trainer.prepare_training_data_from_segmentation(
                segmentation_dir, classification_dir
            )
            print(f"数据准备完成: {dataset_info}")
        else:
            print("未找到分割结果目录，跳过数据准备测试")
    
    except Exception as e:
        print(f"数据准备测试失败: {e}")

if __name__ == "__main__":
    main()
