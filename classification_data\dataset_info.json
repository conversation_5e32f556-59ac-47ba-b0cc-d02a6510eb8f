{"dataset_dir": "classification_data", "classes": ["S0000003-1", "S0000005-1", "S0000009-1", "S0000011-1", "S0000012-1", "S0000014-1", "S0000015-1", "S0000016-1", "S0000023-1", "S0000026-1", "S0000027-1", "S0000029-1", "S0000032-1", "S0000037-1", "S0000040-1", "S0000041-1", "S0000043-1", "S0000044-1", "S0000046-1", "S0000047-1", "S0000049-1", "S0000051-1", "S0000052-1", "S0000055-1", "S0000060-1", "S0000061-1", "S0000062-1", "S0000069-1", "S0000070-1", "S0000071-1", "S0000074-1", "S0000078-1", "S0000079-1", "S0000080-1", "S0000084-1", "S0000085-1", "S0000086-1", "S0000089-1", "S0000090-1", "S0000092-1", "S0000099-1", "S0000100-1", "S0000101-1", "S0000102-1", "S0000110-1", "S0000113-1", "S0000114-1", "S0000118-1", "S0000122-1", "S0000123-1", "S0000124-1", "S0000128-1", "S0000136-1", "S0000140-1", "S0000142-1", "S0000145-1", "S0000148-1", "S0000151-1", "S0000152-1", "S0000154-1", "S0000156-1", "S0000159-1", "S0000160-1", "S0000164-1", "S0000169-1", "S0000172-1", "S0000176-1", "S0000183-1", "S0000186-1", "S0000189-1", "S0000190-1", "S0000193-1", "S0000194-1", "S0000198-1", "S0000199-1", "S0000202-1", "S0000209-1", "S0000214-1", "S0000221-1", "S0000223-1", "S0000224-1", "S0000225-1", "S0000227-1", "S0000229-1", "S0000230-1", "S0000231-1", "S0000232-1", "S0000234-1", "S0000235-1", "S0000236-1", "S0000240-1", "S0000241-1", "S0000242-1", "S0000248-1", "S0000250-1", "S0000251-1", "S0000252-1", "S0000253-1", "S0000254-1", "S0000256-1", "S0000261-1", "S0000268-1", "S0000270-1", "S0000272-1", "S0000273-1", "S0006260-1"], "class_counts": {"S0000003-1": 5, "S0000005-1": 2, "S0000009-1": 2, "S0000011-1": 2, "S0000012-1": 2, "S0000014-1": 5, "S0000015-1": 1, "S0000016-1": 4, "S0000023-1": 3, "S0000026-1": 2, "S0000027-1": 3, "S0000029-1": 3, "S0000032-1": 3, "S0000037-1": 3, "S0000040-1": 2, "S0000041-1": 2, "S0000043-1": 3, "S0000044-1": 3, "S0000046-1": 5, "S0000047-1": 5, "S0000049-1": 5, "S0000051-1": 5, "S0000052-1": 4, "S0000055-1": 5, "S0000060-1": 5, "S0000061-1": 5, "S0000062-1": 4, "S0000069-1": 3, "S0000070-1": 5, "S0000071-1": 5, "S0000074-1": 3, "S0000078-1": 5, "S0000079-1": 4, "S0000080-1": 2, "S0000084-1": 3, "S0000085-1": 3, "S0000086-1": 2, "S0000089-1": 3, "S0000090-1": 4, "S0000092-1": 2, "S0000099-1": 4, "S0000100-1": 5, "S0000101-1": 3, "S0000102-1": 3, "S0000110-1": 5, "S0000113-1": 4, "S0000114-1": 2, "S0000118-1": 4, "S0000122-1": 5, "S0000123-1": 5, "S0000124-1": 2, "S0000128-1": 5, "S0000136-1": 2, "S0000140-1": 5, "S0000142-1": 5, "S0000145-1": 4, "S0000148-1": 5, "S0000151-1": 5, "S0000152-1": 5, "S0000154-1": 4, "S0000156-1": 2, "S0000159-1": 4, "S0000160-1": 4, "S0000164-1": 2, "S0000169-1": 5, "S0000172-1": 2, "S0000176-1": 4, "S0000183-1": 4, "S0000186-1": 5, "S0000189-1": 2, "S0000190-1": 2, "S0000193-1": 3, "S0000194-1": 3, "S0000198-1": 2, "S0000199-1": 1, "S0000202-1": 3, "S0000209-1": 5, "S0000214-1": 3, "S0000221-1": 3, "S0000223-1": 3, "S0000224-1": 3, "S0000225-1": 3, "S0000227-1": 3, "S0000229-1": 3, "S0000230-1": 3, "S0000231-1": 3, "S0000232-1": 3, "S0000234-1": 3, "S0000235-1": 3, "S0000236-1": 3, "S0000240-1": 3, "S0000241-1": 3, "S0000242-1": 3, "S0000248-1": 2, "S0000250-1": 7, "S0000251-1": 1, "S0000252-1": 2, "S0000253-1": 2, "S0000254-1": 2, "S0000256-1": 4, "S0000261-1": 3, "S0000268-1": 4, "S0000270-1": 3, "S0000272-1": 2, "S0000273-1": 2, "S0006260-1": 40}, "total_samples": 394, "num_classes": 106, "task": "seed_classification"}