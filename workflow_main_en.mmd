graph TD
    A[📁 Raw Seed Images<br/>Organized by Class] --> B{🔍 Image Segmentation<br/>Method Selection}
    
    B --> C[🤖 SAM Segmentation<br/>Segment Anything Model]
    B --> D[🎭 Mask-based Segmentation<br/>Traditional CV Methods]
    B --> E[✨ Transparent Background<br/>Segmentation]
    
    C --> F[📊 Segmentation Results<br/>Individual Seed Images]
    D --> F
    E --> F
    
    F --> G{📋 YOLO Annotation<br/>Enabled?}
    G -->|Yes| H[📝 YOLO Annotations<br/>JSON Format]
    G -->|No| I[⚠️ Manual Annotation<br/>Required]
    
    H --> J[🎯 YOLO Dataset Creation<br/>Train/Val/Test Split]
    I --> J
    
    J --> K{🌱 Seed Detection<br/>Optimization Mode}
    K -->|Enabled| L[⚙️ Specialized Parameters<br/>• Single class: 'seed'<br/>• Optimized augmentation<br/>• Early stopping]
    K -->|Disabled| M[📊 Standard YOLO<br/>Training Parameters]
    
    L --> N[🏋️ YOLO Model Training<br/>Object Detection]
    M --> N
    
    N --> O[🎯 Trained YOLO Model<br/>Seed Detection]
    
    F --> P[📂 Classification Data<br/>Preparation]
    P --> Q[🗂️ Class-based Organization<br/>Folder = Class Label]
    Q --> R{🧠 Model Architecture<br/>Selection}
    
    R --> S[🔥 ResNet<br/>18/34/50/101]
    R --> T[⚡ EfficientNet<br/>B0/B1/B2/B3/B4]
    R --> U[📱 MobileNet<br/>V2/V3]
    R --> V[🌐 DenseNet<br/>121/169]
    R --> W[🏛️ VGG<br/>16/19]
    
    S --> X[🏋️ Classification Training<br/>Transfer Learning]
    T --> X
    U --> X
    V --> X
    W --> X
    
    X --> Y[🧠 Trained Classification<br/>Model]
    
    O --> Z[🔄 Integrated Pipeline<br/>Detection + Classification]
    Y --> Z
    
    Z --> AA[📸 New Test Images]
    AA --> BB[🎯 Seed Detection<br/>YOLO Inference]
    BB --> CC[✂️ Crop Detected Seeds<br/>Bounding Box Extraction]
    CC --> DD[🏷️ Seed Classification<br/>Class Prediction]
    DD --> EE[📊 Final Results<br/>• Seed locations<br/>• Class labels<br/>• Confidence scores]
    
    %% Styling
    classDef inputData fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef processing fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef training fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef models fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef results fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef decision fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    
    class A,AA inputData
    class B,C,D,E,F,P,Q,BB,CC,DD processing
    class N,X training
    class O,Y models
    class EE results
    class G,K,R decision
