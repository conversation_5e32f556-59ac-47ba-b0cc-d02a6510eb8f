graph LR
    subgraph "📁 输入数据"
        A1[🌾 wheat/<br/>小麦图像1.jpg<br/>小麦图像2.jpg]
        A2[🌽 corn/<br/>玉米图像1.jpg<br/>玉米图像2.jpg]
        A3[🍚 rice/<br/>水稻图像1.jpg<br/>水稻图像2.jpg]
        A4[🫘 soybean/<br/>大豆图像1.jpg<br/>大豆图像2.jpg]
    end
    
    subgraph "🔍 分割输出"
        B1[📂 wheat/<br/>• seed_1.jpg<br/>• seed_2.jpg<br/>• transparent_segmentation.png<br/>• yolo_annotations.json]
        B2[📂 corn/<br/>• seed_1.jpg<br/>• seed_2.jpg<br/>• transparent_segmentation.png<br/>• yolo_annotations.json]
        B3[📂 rice/<br/>• seed_1.jpg<br/>• seed_2.jpg<br/>• transparent_segmentation.png<br/>• yolo_annotations.json]
        B4[📂 soybean/<br/>• seed_1.jpg<br/>• seed_2.jpg<br/>• transparent_segmentation.png<br/>• yolo_annotations.json]
    end
    
    subgraph "🎯 YOLO数据集"
        C1[📁 images/<br/>train/ 训练<br/>val/ 验证<br/>test/ 测试]
        C2[📁 labels/<br/>train/ 训练标签<br/>val/ 验证标签<br/>test/ 测试标签]
        C3[📄 dataset.yaml<br/>nc: 1 类别数<br/>names: seed 类别名<br/>train: ./train 训练路径<br/>val: ./val 验证路径]
    end
    
    subgraph "🗂️ 分类数据集"
        D1[📂 wheat/<br/>wheat_seed_1.jpg<br/>wheat_seed_2.jpg]
        D2[📂 corn/<br/>corn_seed_1.jpg<br/>corn_seed_2.jpg]
        D3[📂 rice/<br/>rice_seed_1.jpg<br/>rice_seed_2.jpg]
        D4[📂 soybean/<br/>soybean_seed_1.jpg<br/>soybean_seed_2.jpg]
        D5[📄 dataset_info.json<br/>classes: wheat, corn, rice, soybean<br/>num_classes: 4 类别数<br/>total_samples: N 总样本数]
    end
    
    subgraph "🤖 训练好的模型"
        E1[🎯 YOLO模型<br/>best.pt 最佳模型<br/>last.pt 最后模型<br/>• 检测种子位置<br/>• 单类别: 'seed']
        E2[🧠 分类模型<br/>resnet18_trained.pth<br/>efficientnet_trained.pth<br/>• 分类种子类型<br/>• 多类别输出]
    end
    
    subgraph "📊 推理管道"
        F1[📸 新图像<br/>test_image.jpg]
        F2[🎯 检测结果<br/>• 边界框<br/>• 置信度分数<br/>• 坐标信息]
        F3[✂️ 裁剪种子<br/>crop_1.jpg<br/>crop_2.jpg<br/>crop_3.jpg]
        F4[🏷️ 分类结果<br/>• 类别预测<br/>• 置信度分数<br/>• 每个种子标签]
        F5[📋 最终输出<br/>• 检测框<br/>• 类别标签<br/>• 置信度分数<br/>• 每类种子计数]
    end
    
    %% 数据流连接
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    
    B1 --> C1
    B2 --> C1
    B3 --> C1
    B4 --> C1
    
    B1 --> C2
    B2 --> C2
    B3 --> C2
    B4 --> C2
    
    C1 --> C3
    C2 --> C3
    
    B1 --> D1
    B2 --> D2
    B3 --> D3
    B4 --> D4
    
    D1 --> D5
    D2 --> D5
    D3 --> D5
    D4 --> D5
    
    C3 --> E1
    D5 --> E2
    
    F1 --> F2
    F2 --> F3
    F3 --> F4
    F4 --> F5
    
    E1 -.-> F2
    E2 -.-> F4
    
    %% 样式设置
    classDef input fill:#e3f2fd,stroke:#0277bd,stroke-width:2px,color:#000
    classDef segmentation fill:#f1f8e9,stroke:#388e3c,stroke-width:2px,color:#000
    classDef dataset fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef models fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef inference fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4 input
    class B1,B2,B3,B4 segmentation
    class C1,C2,C3,D1,D2,D3,D4,D5 dataset
    class E1,E2 models
    class F1,F2,F3,F4,F5 inference
